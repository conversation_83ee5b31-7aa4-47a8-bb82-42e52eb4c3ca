# 🚀 Pull Request Template – ClubVerse

> **Please do not submit a pull request until all tests and build checks pass locally and in CI. PRs with failing checks will not be reviewed.**

## 📋 Description

<!-- Briefly describe what this PR does and why. Focus on the user or developer impact. -->

## 🎯 What does this PR do?
<!-- Main purpose and summary of the change. -->

## 🔗 Related Issues
<!-- Link related issues (e.g. #Closes(15)) -->
#Closes (add_issue_number)

## 🧩 Issue Complexity Level
- [ ] 🌿 Level 1 – Simple bug fix / minor enhancement
- [ ] 🌊 Level 2 – Moderate logic / new component / multi-file changes
- [ ] 🔥 Level 3 – Complex logic / large-scale refactor / critical feature

## 🧪 Type of Change
<!-- Mark all that apply with an 'x' -->
- [ ] 🐛 Bug fix
- [ ] ✨ New feature
- [ ] 💥 Breaking change
- [ ] 📚 Documentation
- [ ] 🎨 UI/UX improvement
- [ ] ⚡ Performance
- [ ] 🔒 Security
- [ ] 🧹 Refactor
- [ ] 🧪 Tests
- [ ] 🔧 Config/DevOps

## 👥 User Roles Impacted
<!-- Mark all that apply -->
- [ ] 👑 Super Admin
- [ ] 🏫 College Admin
- [ ] 🎯 Club Admin
- [ ] 👨‍🎓 Student
- [ ] 🔐 Auth
- [ ] 🌐 General UI/UX

## 📁 Main Files Changed
<!-- List key files and a one-line summary for each -->
- `path/to/file.tsx` – What changed?

## 🧪 Testing & Verification
- [ ] Unit tests pass
- [ ] Integration/E2E tests pass
- [ ] Manual testing done
- [ ] No regressions found

### Key Test Scenarios (mark those tested)
- [ ] Authentication/Authorization
- [ ] Role-based access
- [ ] Responsive design
- [ ] Form validation
- [ ] Real-time features (chat, notifications)
- [ ] Event/Club management
- [ ] Image/file upload

## 📸 Screenshots (if UI change)
<!-- Before/after screenshots or videos -->

## 🏗️ Technical Details
- **Architecture/Patterns:** <!-- Note any new patterns or refactors -->
- **API Changes:** <!-- List new/changed endpoints -->
- **DB Changes:** <!-- List schema/collection changes -->
- **Dependencies:** <!-- List new/removed/updated packages -->

## 🚨 Breaking Changes
<!-- List any breaking changes and migration steps -->

## 🔒 Security Considerations
- [ ] No sensitive data exposed
- [ ] Auth checks in place
- [ ] Input validation/sanitization
- [ ] No new XSS/SQLi risks

## 📊 Performance Impact
- [ ] No significant slowdown
- [ ] Bundle size checked
- [ ] DB queries optimized

## 🧹 Code Quality
- [ ] TypeScript best practices
- [ ] No console.logs in production
- [ ] Error handling present
- [ ] Accessibility (a11y) considered
- [ ] Component props typed
- [ ] Custom hooks follow React best practices

## 📝 Documentation
- [ ] Code is self-explanatory or commented
- [ ] README/docs updated if needed
- [ ] API docs updated if needed

## 🚀 Deployment Notes
- [ ] Env vars updated (if needed)
- [ ] DB migrations ready (if needed)
- [ ] Feature flags/monitoring updated (if needed)

## 📋 Final Checklist
- [ ] Self-reviewed
- [ ] All tests pass
- [ ] Style guidelines followed
- [ ] Docs updated
- [ ] No sensitive info in PR
- [ ] Clear commit messages
- [ ] PR title/description are informative

## 🎯 Review Guidelines
- **Functionality:** Does it work as intended?
- **Security:** Any vulnerabilities?
- **Performance:** Any slowdowns?
- **Maintainability:** Is it easy to understand/extend?
- **Testing:** Are there enough tests?
- **Docs:** Is it well-documented?

## 💬 Additional Notes
<!-- Anything else for reviewers? --> 
